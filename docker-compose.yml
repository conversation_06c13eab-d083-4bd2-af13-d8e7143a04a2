
services:
  postgres:
    image: postgres:latest
    container_name: postgres
    restart: always
    env_file: .env
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:latest
    container_name: redis
    restart: always
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data

  minio:
    image: minio/minio:latest
    container_name: minio
    restart: always
    env_file: .env
    command: server /data --console-address ":9001"
    ports:
      - "${MINIO_PORT}:9000"
      - "${MINIO_CONSOLE_PORT}:9001"
    volumes:
      - minio_data:/data

volumes:
  postgres_data:
  redis_data:
  minio_data:
